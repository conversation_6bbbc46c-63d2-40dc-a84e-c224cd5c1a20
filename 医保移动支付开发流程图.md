# 医保移动支付开发流程图

## 1. 准备阶段流程图

```mermaid
graph TD
    A[开始] --> B[国家局接入审批]
    B --> C{审批通过?}
    C -->|是| D[获取国家局参数]
    C -->|否| E[修改申请重新提交]
    E --> B
    
    D --> F[省中台环境登记确认]
    F --> G[微信测试环境准备]
    G --> H[电子凭证测试环境准备]
    
    H --> I[开始并行开发]
    
    style D fill:#e1f5fe
    style F fill:#fff3e0
    style G fill:#f3e5f5
    style H fill:#e8f5e8
```

## 2. 并行开发阶段详细流程

### 2.1 授权调试阶段（可并行工作）

```mermaid
graph LR
    A[国家局审批通过] --> B[获取参数]
    
    B --> C[小程序/公众号授权对接]
    B --> D[HIS费用明细上传接口改造]
    B --> E[前后端接口约定]
    B --> F[网络策略打通]
    
    C --> G[用户授权流程开发]
    D --> H[加解密处理]
    D --> I[使用腾讯SDK]
    E --> J[传参和调用约定]
    F --> K[医保访问医院网络策略]
    
    style C fill:#e3f2fd
    style D fill:#fff8e1
    style E fill:#f1f8e9
    style F fill:#fce4ec
```

### 2.2 授权调通后阶段（可并行工作）

```mermaid
graph LR
    A[授权调试完成] --> B[小程序/公众号下单接口研究]
    A --> C[HIS支付下单接口调试]
    
    B --> D[完成前置开发工作]
    B --> E[完成下单签名工作]
    B --> F[等待HIS改造完成]
    
    C --> G[调试支付下单接口]
    C --> H[提供院内地址给中台配置]
    
    style B fill:#e8f5e8
    style C fill:#fff3e0
```

### 2.3 退款流程阶段（可并行工作）

```mermaid
graph LR
    A[退款需求] --> B[医保先退再自费退]
    
    B --> C[HIS对接中台退款接口]
    B --> D[小程序/公众号对接微信退款接口]
    
    C --> E[调用6203医保退费接口]
    D --> F[调用微信退款接口]
    
    E --> G[确认医保部分已退款]
    G --> F
    
    style C fill:#fff8e1
    style D fill:#e3f2fd
```

## 3. 完整开发时序图

```mermaid
sequenceDiagram
    participant 国家局 as 国家局
    participant 省中台 as 省中台
    participant 微信 as 微信
    participant 医院 as 医院
    participant 小程序 as 小程序/公众号
    participant HIS as HIS系统
    
    Note over 国家局,HIS: 准备阶段
    医院->>国家局: 申请医保移动支付权限
    国家局-->>医院: 返回参数（机构编码、业务类型等）
    
    医院->>省中台: 确认测试环境登记
    省中台-->>医院: 确认已登记
    
    医院->>微信: 申请测试环境权限
    微信-->>医院: 提供测试环境和技术群
    
    Note over 国家局,HIS: 并行开发阶段1：授权调试
    par 小程序授权开发
        小程序->>微信: 开发用户授权接口
        微信-->>小程序: 返回payAuthNo和经纬度
    and HIS接口改造
        HIS->>省中台: 改造费用明细上传接口
        HIS->>HIS: 处理加解密（使用SDK）
    and 接口约定
        小程序->>HIS: 约定传参和调用方式
    and 网络配置
        医院->>省中台: 配置网络策略
    end
    
    Note over 国家局,HIS: 并行开发阶段2：支付下单
    par 小程序下单开发
        小程序->>小程序: 研究下单接口
        小程序->>小程序: 完成签名工作
    and HIS下单调试
        HIS->>省中台: 调试支付下单接口
        HIS->>省中台: 提供回调地址配置
    end
    
    Note over 国家局,HIS: 并行开发阶段3：退款流程
    par 退款接口开发
        HIS->>省中台: 对接医保退款接口
    and 微信退款
        小程序->>微信: 对接微信退款接口
    end
```

## 4. 关键接口开发清单

### 4.1 小程序/公众号端需要开发的接口

| 接口类型 | 接口说明 | 开发重点 | 并行阶段 |
|---------|---------|---------|---------|
| **用户授权接口** | 获取payAuthNo和用户信息 | 链接拼接、参数校验 | 阶段1 |
| **位置获取接口** | 获取用户经纬度 | 权限处理、格式转换 | 阶段1 |
| **订单查询接口** | 获取待缴费订单列表 | 与HIS接口对接 | 阶段1 |
| **支付下单接口** | 创建医保支付订单 | 签名算法、参数传递 | 阶段2 |
| **支付跳转接口** | 跳转医保支付小程序 | pay_appid、pay_url处理 | 阶段2 |
| **支付结果查询** | 查询支付状态 | 轮询机制、状态处理 | 阶段2 |
| **退款接口** | 处理退款请求 | 先医保后自费 | 阶段3 |

### 4.2 HIS系统需要开发的接口

| 接口类型 | 接口说明 | 开发重点 | 并行阶段 |
|---------|---------|---------|---------|
| **费用明细上传** | 6201接口改造 | 加解密、payAuthNo处理 | 阶段1 |
| **人员信息获取** | 1101接口调用 | psnNo获取 | 阶段1 |
| **门诊登记** | 2201接口调用 | 就诊ID获取 | 阶段1 |
| **支付下单** | 6202接口调用 | 分账结果处理 | 阶段2 |
| **支付结果查询** | 6301接口调用 | 主动查询机制 | 阶段2 |
| **支付结果通知** | 6302接口接收 | 回调地址配置 | 阶段2 |
| **医保退费** | 6203接口调用 | 退费流程处理 | 阶段3 |

## 5. 关键注意事项

### 5.1 环境准备要求
- ✅ **省中台登记确认**：测试前必须确认医院已在省中台测试环境登记
- ✅ **微信测试环境**：测试人员微信号需要切换到测试环境
- ✅ **医保测试数据**：国家局医保电子凭证测试环境需要有参保数据
- ✅ **网络策略配置**：医保访问医院网络的策略需要提前配置

### 5.2 开发关键点
- ✅ **payAuthNo一致性**：费用明细上传和微信下单使用的payAuthNo必须一致
- ✅ **用户经纬度**：国标2.0必填参数，需要正确获取和传递
- ✅ **加解密处理**：使用国密算法SM2和SM4，建议使用腾讯SDK
- ✅ **签名算法**：微信支付签名和医保接口签名分别处理

### 5.3 并行开发优势
- 🚀 **时间节省**：多个模块同时开发，缩短整体开发周期
- 🚀 **风险分散**：各模块独立开发，降低相互依赖风险
- 🚀 **资源优化**：前端、后端、网络配置可以同时进行
- 🚀 **问题早发现**：并行开发有助于提前发现接口对接问题

## 6. 开发时间规划

| 阶段 | 工作内容 | 预计时间 | 可并行程度 |
|------|---------|---------|-----------|
| **准备阶段** | 申请、登记、环境配置 | 2-3周 | 部分并行 |
| **授权调试阶段** | 用户授权、HIS改造、接口约定 | 1-2周 | 高度并行 |
| **支付下单阶段** | 下单接口、签名处理 | 1-2周 | 高度并行 |
| **退款流程阶段** | 退款接口开发 | 1周 | 高度并行 |
| **联调测试** | 端到端测试 | 1-2周 | 部分并行 |

**总计开发时间：6-10周**（通过并行开发可以显著缩短时间）
