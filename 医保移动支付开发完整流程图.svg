<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 1206 4863.75" style="max-width: 1206px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-4cac51e6-4012-46b1-8707-19433cfe9590"><style>#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#333;}#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 .error-icon{fill:#552222;}#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 .error-text{fill:#552222;stroke:#552222;}#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 .edge-thickness-normal{stroke-width:1px;}#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 .marker{fill:#333333;stroke:#333333;}#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 .marker.cross{stroke:#333333;}#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 p{margin:0;}#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#333;}#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 .cluster-label text{fill:#333;}#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 .cluster-label span{color:#333;}#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 .cluster-label span p{background-color:transparent;}#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 .label text,#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 span{fill:#333;color:#333;}#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 .node rect,#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 .node circle,#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 .node ellipse,#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 .node polygon,#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 .node path{fill:#ECECFF;stroke:#9370DB;stroke-width:1px;}#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 .rough-node .label text,#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 .node .label text,#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 .image-shape .label,#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 .icon-shape .label{text-anchor:middle;}#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 .rough-node .label,#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 .node .label,#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 .image-shape .label,#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 .icon-shape .label{text-align:center;}#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 .node.clickable{cursor:pointer;}#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 .root .anchor path{fill:#333333!important;stroke-width:0;stroke:#333333;}#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 .arrowheadPath{fill:#333333;}#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 .edgePath .path{stroke:#333333;stroke-width:2.0px;}#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 .flowchart-link{stroke:#333333;fill:none;}#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 .edgeLabel{background-color:rgba(232,232,232, 0.8);text-align:center;}#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 .edgeLabel p{background-color:rgba(232,232,232, 0.8);}#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 .edgeLabel rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 .labelBkg{background-color:rgba(232, 232, 232, 0.5);}#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 .cluster rect{fill:#ffffde;stroke:#aaaa33;stroke-width:1px;}#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 .cluster text{fill:#333;}#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 .cluster span{color:#333;}#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(80, 100%, 96.2745098039%);border:1px solid #aaaa33;border-radius:2px;pointer-events:none;z-index:100;}#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#333;}#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 rect.text{fill:none;stroke-width:0;}#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 .icon-shape,#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 .image-shape{background-color:rgba(232,232,232, 0.8);text-align:center;}#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 .icon-shape p,#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 .image-shape p{background-color:rgba(232,232,232, 0.8);padding:2px;}#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 .icon-shape rect,#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 .image-shape rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"><g data-look="classic" id="subGraph6" class="cluster"><rect height="768" width="330" y="4087.75" x="578" style=""></rect><g transform="translate(669.453125, 4087.75)" class="cluster-label"><foreignObject height="24" width="147.09375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>阶段7: 正式环境上线</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph5" class="cluster"><rect height="949.875" width="480" y="3087.875" x="408" style=""></rect><g transform="translate(574.453125, 3087.875)" class="cluster-label"><foreignObject height="24" width="147.09375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>阶段6: 系统联调测试</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph4" class="cluster"><rect height="464" width="1054.65625" y="2573.875" x="76.5" style=""></rect><g transform="translate(503.828125, 2573.875)" class="cluster-label"><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>阶段5: 并行开发阶段3 - 退款功能</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph3" class="cluster"><rect height="360" width="1184" y="2163.875" x="8" style=""></rect><g transform="translate(500, 2163.875)" class="cluster-label"><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>阶段4: 并行开发阶段2 - 支付下单功能</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph2" class="cluster"><rect height="408" width="950" y="1705.875" x="248" style=""></rect><g transform="translate(623, 1705.875)" class="cluster-label"><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>阶段3: 并行开发阶段1 - 用户授权功能</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph1" class="cluster"><rect height="560" width="330" y="1095.875" x="443" style=""></rect><g transform="translate(534.453125, 1095.875)" class="cluster-label"><foreignObject height="24" width="147.09375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>阶段2: 测试环境准备</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph0" class="cluster"><rect height="1037.875" width="606" y="8" x="167" style=""></rect><g transform="translate(396.453125, 8)" class="cluster-label"><foreignObject height="24" width="147.09375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>阶段1: 申请准备阶段</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_B_0" d="M465,87L465,91.167C465,95.333,465,103.667,465,111.333C465,119,465,126,465,129.5L465,133"></path><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_C_1" d="M541.172,215L549.31,219.167C557.448,223.333,573.724,231.667,581.932,239.417C590.141,247.167,590.281,254.334,590.351,257.917L590.422,261.501"></path><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_D_2" d="M544.29,391.165L528.075,404.95C511.86,418.735,479.43,446.305,450.353,467.91C421.275,489.516,395.55,505.156,382.688,512.977L369.826,520.797"></path><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_B_3" d="M299.796,522.875L293.08,514.708C286.364,506.542,272.932,490.208,266.216,461.552C259.5,432.896,259.5,391.917,259.5,352.938C259.5,313.958,259.5,276.979,272.242,254.521C284.985,232.063,310.47,224.126,323.212,220.158L335.954,216.189"></path><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_E_4" d="M601.476,426.399L602.563,434.312C603.65,442.225,605.825,458.05,606.913,471.462C608,484.875,608,495.875,608,501.375L608,506.875"></path><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_F_5" d="M608,588.875L608,593.042C608,597.208,608,605.542,608,613.208C608,620.875,608,627.875,608,631.375L608,634.875"></path><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F_G_6" d="M608,740.875L608,745.042C608,749.208,608,757.542,608,765.208C608,772.875,608,779.875,608,783.375L608,786.875"></path><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G_H_7" d="M608,892.875L608,897.042C608,901.208,608,909.542,608,917.208C608,924.875,608,931.875,608,935.375L608,938.875"></path><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_H_I_8" d="M608,1020.875L608,1025.042C608,1029.208,608,1037.542,608,1045.875C608,1054.208,608,1062.542,608,1070.875C608,1079.208,608,1087.542,608,1095.208C608,1102.875,608,1109.875,608,1113.375L608,1116.875"></path><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I_J_9" d="M608,1222.875L608,1227.042C608,1231.208,608,1239.542,608,1247.208C608,1254.875,608,1261.875,608,1265.375L608,1268.875"></path><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_J_K_10" d="M608,1374.875L608,1379.042C608,1383.208,608,1391.542,608,1399.208C608,1406.875,608,1413.875,608,1417.375L608,1420.875"></path><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_K_L_11" d="M608,1502.875L608,1507.042C608,1511.208,608,1519.542,608,1527.208C608,1534.875,608,1541.875,608,1545.375L608,1548.875"></path><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_L_M_12" d="M608,1630.875L608,1635.042C608,1639.208,608,1647.542,608,1655.875C608,1664.208,608,1672.542,608,1680.875C608,1689.208,608,1697.542,608,1705.208C608,1712.875,608,1719.875,608,1723.375L608,1726.875"></path><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_M_N_13" d="M686,1767.419L743.833,1774.495C801.667,1781.571,917.333,1795.723,975.167,1808.299C1033,1820.875,1033,1831.875,1033,1837.375L1033,1842.875"></path><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_M_O_14" d="M667.712,1784.875L676.926,1789.042C686.141,1793.208,704.571,1801.542,713.785,1809.208C723,1816.875,723,1823.875,723,1827.375L723,1830.875"></path><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_M_P_15" d="M530,1778.675L510.5,1783.875C491,1789.075,452,1799.475,432.5,1810.175C413,1820.875,413,1831.875,413,1837.375L413,1842.875"></path><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_N_Q_16" d="M1033,1948.875L1033,1955.042C1033,1961.208,1033,1973.542,1033,1985.208C1033,1996.875,1033,2007.875,1033,2013.375L1033,2018.875"></path><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_O_R_17" d="M723,1960.875L723,1965.042C723,1969.208,723,1977.542,723,1985.208C723,1992.875,723,1999.875,723,2003.375L723,2006.875"></path><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_P_S_18" d="M413,1948.875L413,1955.042C413,1961.208,413,1973.542,413,1985.208C413,1996.875,413,2007.875,413,2013.375L413,2018.875"></path><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Q_T_19" d="M1033,2076.875L1033,2083.042C1033,2089.208,1033,2101.542,1033,2111.875C1033,2122.208,1033,2130.542,1033,2138.875C1033,2147.208,1033,2155.542,1004.989,2164.873C976.978,2174.205,920.956,2184.536,892.945,2189.701L864.934,2194.866"></path><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_R_T_20" d="M723,2088.875L723,2093.042C723,2097.208,723,2105.542,723,2113.875C723,2122.208,723,2130.542,723,2138.875C723,2147.208,723,2155.542,724.928,2163.288C726.855,2171.034,730.71,2178.194,732.638,2181.773L734.565,2185.353"></path><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_S_T_21" d="M413,2076.875L413,2083.042C413,2089.208,413,2101.542,413,2111.875C413,2122.208,413,2130.542,413,2138.875C413,2147.208,413,2155.542,450.341,2165.453C487.682,2175.365,562.364,2186.854,599.705,2192.599L637.047,2198.344"></path><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_T_U_22" d="M832.519,2242.875L845.099,2247.042C857.679,2251.208,882.84,2259.542,895.42,2267.208C908,2274.875,908,2281.875,908,2285.375L908,2288.875"></path><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_T_V_23" d="M641,2229.397L588.833,2235.81C536.667,2242.223,432.333,2255.049,380.167,2264.962C328,2274.875,328,2281.875,328,2285.375L328,2288.875"></path><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_U_W_24" d="M990.266,2370.875L999.055,2375.042C1007.844,2379.208,1025.422,2387.542,1034.211,2397.208C1043,2406.875,1043,2417.875,1043,2423.375L1043,2428.875"></path><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_U_X_25" d="M825.734,2370.875L816.945,2375.042C808.156,2379.208,790.578,2387.542,781.789,2397.208C773,2406.875,773,2417.875,773,2423.375L773,2428.875"></path><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_V_Y_26" d="M422.453,2370.875L432.544,2375.042C442.635,2379.208,462.818,2387.542,472.909,2395.208C483,2402.875,483,2409.875,483,2413.375L483,2416.875"></path><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_V_Z_27" d="M233.547,2370.875L223.456,2375.042C213.365,2379.208,193.182,2387.542,183.091,2395.208C173,2402.875,173,2409.875,173,2413.375L173,2416.875"></path><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_W_AA_28" d="M1043,2486.875L1043,2493.042C1043,2499.208,1043,2511.542,1043,2521.875C1043,2532.208,1043,2540.542,1043,2548.875C1043,2557.208,1043,2565.542,1009.324,2575.546C975.647,2585.55,908.294,2597.224,874.618,2603.061L840.941,2608.899"></path><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_X_AA_29" d="M773,2486.875L773,2493.042C773,2499.208,773,2511.542,773,2521.875C773,2532.208,773,2540.542,773,2548.875C773,2557.208,773,2565.542,770.929,2573.298C768.859,2581.053,764.717,2588.232,762.647,2591.821L760.576,2595.41"></path><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Y_AA_30" d="M483,2498.875L483,2503.042C483,2507.208,483,2515.542,483,2523.875C483,2532.208,483,2540.542,483,2548.875C483,2557.208,483,2565.542,510.013,2575.111C537.026,2584.68,591.052,2595.485,618.065,2600.888L645.078,2606.291"></path><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Z_AA_31" d="M173,2498.875L173,2503.042C173,2507.208,173,2515.542,173,2523.875C173,2532.208,173,2540.542,173,2548.875C173,2557.208,173,2565.542,251.669,2576.885C330.339,2588.229,487.678,2602.582,566.347,2609.759L645.017,2616.936"></path><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AA_BB_32" d="M837,2646.958L859.974,2652.111C882.948,2657.264,928.896,2667.569,951.87,2676.222C974.844,2684.875,974.844,2691.875,974.844,2695.375L974.844,2698.875"></path><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AA_CC_33" d="M649,2635.394L579.083,2642.474C509.167,2649.554,369.333,2663.715,299.417,2674.295C229.5,2684.875,229.5,2691.875,229.5,2695.375L229.5,2698.875"></path><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BB_DD_34" d="M974.844,2780.875L974.844,2785.042C974.844,2789.208,974.844,2797.542,974.844,2805.208C974.844,2812.875,974.844,2819.875,974.844,2823.375L974.844,2826.875"></path><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CC_EE_35" d="M229.5,2780.875L229.5,2785.042C229.5,2789.208,229.5,2797.542,229.5,2805.208C229.5,2812.875,229.5,2819.875,229.5,2823.375L229.5,2826.875"></path><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DD_FF_36" d="M974.844,2884.875L974.844,2889.042C974.844,2893.208,974.844,2901.542,956.512,2910.769C938.181,2919.996,901.518,2930.116,883.187,2935.177L864.856,2940.237"></path><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_EE_FF_37" d="M229.5,2884.875L229.5,2889.042C229.5,2893.208,229.5,2901.542,294.755,2913.841C360.01,2926.141,490.52,2942.407,555.776,2950.54L621.031,2958.673"></path><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_FF_GG_38" d="M743,3012.875L743,3017.042C743,3021.208,743,3029.542,743,3037.875C743,3046.208,743,3054.542,743,3062.875C743,3071.208,743,3079.542,743,3087.208C743,3094.875,743,3101.875,743,3105.375L743,3108.875"></path><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_GG_HH_39" d="M743,3166.875L743,3171.042C743,3175.208,743,3183.542,743,3191.208C743,3198.875,743,3205.875,743,3209.375L743,3212.875"></path><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_HH_II_40" d="M743,3294.875L743,3299.042C743,3303.208,743,3311.542,743,3319.208C743,3326.875,743,3333.875,743,3337.375L743,3340.875"></path><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_II_JJ_41" d="M743,3422.875L743,3427.042C743,3431.208,743,3439.542,743,3447.208C743,3454.875,743,3461.875,743,3465.375L743,3468.875"></path><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_JJ_KK_42" d="M743,3550.875L743,3555.042C743,3559.208,743,3567.542,743,3575.208C743,3582.875,743,3589.875,743,3593.375L743,3596.875"></path><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_KK_LL_43" d="M743,3678.875L743,3683.042C743,3687.208,743,3695.542,743.07,3703.292C743.141,3711.042,743.281,3718.209,743.351,3721.792L743.422,3725.376"></path><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LL_MM_44" d="M698.995,3840.745L680.83,3854.246C662.664,3867.747,626.332,3894.748,602.748,3913.933C579.165,3933.118,568.329,3944.486,562.912,3950.17L557.494,3955.855"></path><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MM_HH_45" d="M495.883,3958.75L488.319,3952.583C480.755,3946.417,465.628,3934.083,458.064,3908.76C450.5,3883.438,450.5,3845.125,450.5,3808.813C450.5,3772.5,450.5,3738.188,450.5,3710.365C450.5,3682.542,450.5,3661.208,450.5,3639.875C450.5,3618.542,450.5,3597.208,450.5,3575.875C450.5,3554.542,450.5,3533.208,450.5,3511.875C450.5,3490.542,450.5,3469.208,450.5,3447.875C450.5,3426.542,450.5,3405.208,450.5,3383.875C450.5,3362.542,450.5,3341.208,481.599,3323.737C512.697,3306.266,574.895,3292.657,605.994,3285.852L637.092,3279.048"></path><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LL_NN_46" d="M743.5,3885.25L743.417,3891.333C743.333,3897.417,743.167,3909.583,743.083,3921.167C743,3932.75,743,3943.75,743,3949.25L743,3954.75"></path><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_NN_OO_47" d="M743,4012.75L743,4016.917C743,4021.083,743,4029.417,743,4037.75C743,4046.083,743,4054.417,743,4062.75C743,4071.083,743,4079.417,743,4087.083C743,4094.75,743,4101.75,743,4105.25L743,4108.75"></path><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_OO_PP_48" d="M743,4190.75L743,4194.917C743,4199.083,743,4207.417,743,4215.083C743,4222.75,743,4229.75,743,4233.25L743,4236.75"></path><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_PP_QQ_49" d="M743,4318.75L743,4322.917C743,4327.083,743,4335.417,743,4343.083C743,4350.75,743,4357.75,743,4361.25L743,4364.75"></path><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_QQ_RR_50" d="M743,4470.75L743,4474.917C743,4479.083,743,4487.417,743,4495.083C743,4502.75,743,4509.75,743,4513.25L743,4516.75"></path><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_RR_SS_51" d="M743,4622.75L743,4626.917C743,4631.083,743,4639.417,743,4647.083C743,4654.75,743,4661.75,743,4665.25L743,4668.75"></path><path marker-end="url(#mermaid-4cac51e6-4012-46b1-8707-19433cfe9590_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SS_TT_52" d="M743,4726.75L743,4730.917C743,4735.083,743,4743.417,743,4751.083C743,4758.75,743,4765.75,743,4769.25L743,4772.75"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(447, 473.875)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(608, 473.875)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(590, 3921.75)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(743, 3921.75)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(465, 60)" id="flowchart-A-411" class="node default"><rect height="54" width="124" y="-27" x="-62" style="" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>项目启动</p></span></div></foreignObject></g></g><g transform="translate(465, 176)" id="flowchart-B-412" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"></rect><g transform="translate(-100, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>向国家医保局申请医保移动支付权限</p></span></div></foreignObject></g></g><g transform="translate(590, 350.9375)" id="flowchart-C-414" class="node default"><polygon transform="translate(-85.9375,85.9375)" class="label-container" points="85.9375,0 171.875,-85.9375 85.9375,-171.875 0,-85.9375"></polygon><g transform="translate(-58.9375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="117.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>国家局审批通过?</p></span></div></foreignObject></g></g><g transform="translate(322, 549.875)" id="flowchart-D-416" class="node default"><rect height="54" width="220" y="-27" x="-110" style="" class="basic label-container"></rect><g transform="translate(-80, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="160"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>修改申请材料重新提交</p></span></div></foreignObject></g></g><g transform="translate(608, 549.875)" id="flowchart-E-420" class="node default"><rect height="78" width="252" y="-39" x="-126" style="fill:#e1f5fe !important" class="basic label-container"></rect><g transform="translate(-96, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="192"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>获得国家局参数<br>机构编码、业务类型编码等</p></span></div></foreignObject></g></g><g transform="translate(608, 689.875)" id="flowchart-F-422" class="node default"><rect height="102" width="260" y="-51" x="-130" style="fill:#fff3e0 !important" class="basic label-container"></rect><g transform="translate(-100, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>联系省医保中台确认医院已登记<br>确保授权时不会报错</p></span></div></foreignObject></g></g><g transform="translate(608, 841.875)" id="flowchart-G-424" class="node default"><rect height="102" width="260" y="-51" x="-130" style="fill:#f3e5f5 !important" class="basic label-container"></rect><g transform="translate(-100, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>向微信申请医保支付测试环境权限<br>获得测试环境商户号</p></span></div></foreignObject></g></g><g transform="translate(608, 981.875)" id="flowchart-H-426" class="node default"><rect height="78" width="236" y="-39" x="-118" style="" class="basic label-container"></rect><g transform="translate(-88, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="176"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>申请微信小程序认证<br>完成小程序与商户号绑定</p></span></div></foreignObject></g></g><g transform="translate(608, 1171.875)" id="flowchart-I-428" class="node default"><rect height="102" width="260" y="-51" x="-130" style="fill:#e8f5e8 !important" class="basic label-container"></rect><g transform="translate(-100, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>添加测试人员微信号到测试白名单<br>联系微信技术人员处理</p></span></div></foreignObject></g></g><g transform="translate(608, 1323.875)" id="flowchart-J-430" class="node default"><rect height="102" width="260" y="-51" x="-130" style="fill:#fce4ec !important" class="basic label-container"></rect><g transform="translate(-100, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>在国家局医保电子凭证测试环境<br>添加测试人员参保数据</p></span></div></foreignObject></g></g><g transform="translate(608, 1463.875)" id="flowchart-K-432" class="node default"><rect height="78" width="252" y="-39" x="-126" style="" class="basic label-container"></rect><g transform="translate(-96, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="192"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>配置医院网络防火墙<br>允许医保中台访问医院系统</p></span></div></foreignObject></g></g><g transform="translate(608, 1591.875)" id="flowchart-L-434" class="node default"><rect height="78" width="236" y="-39" x="-118" style="" class="basic label-container"></rect><g transform="translate(-88, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="176"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>准备测试用的处方单数据<br>确保有可测试的订单</p></span></div></foreignObject></g></g><g transform="translate(608, 1757.875)" id="flowchart-M-436" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>开始并行开发</p></span></div></foreignObject></g></g><g transform="translate(1033, 1897.875)" id="flowchart-N-438" class="node default"><rect height="102" width="260" y="-51" x="-130" style="fill:#e3f2fd !important" class="basic label-container"></rect><g transform="translate(-100, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>小程序开发用户授权功能<br>获取payAuthNo和用户经纬度</p></span></div></foreignObject></g></g><g transform="translate(723, 1897.875)" id="flowchart-O-440" class="node default"><rect height="126" width="260" y="-63" x="-130" style="fill:#fff8e1 !important" class="basic label-container"></rect><g transform="translate(-100, -48)" style="" class="label"><rect></rect><foreignObject height="96" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>医院HIS系统改造费用明细上传接口<br>6201接口支持payAuthNo参数</p></span></div></foreignObject></g></g><g transform="translate(413, 1897.875)" id="flowchart-P-442" class="node default"><rect height="102" width="260" y="-51" x="-130" style="" class="basic label-container"></rect><g transform="translate(-100, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>小程序与HIS系统约定接口规范<br>确定参数传递方式</p></span></div></foreignObject></g></g><g transform="translate(1033, 2049.875)" id="flowchart-Q-444" class="node default"><rect height="54" width="252" y="-27" x="-126" style="" class="basic label-container"></rect><g transform="translate(-96, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="192"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>完成微信医保授权页面对接</p></span></div></foreignObject></g></g><g transform="translate(723, 2049.875)" id="flowchart-R-446" class="node default"><rect height="78" width="214.71875" y="-39" x="-107.359375" style="" class="basic label-container"></rect><g transform="translate(-77.359375, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="154.71875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>完成加密解密处理<br>可使用腾讯提供的SDK</p></span></div></foreignObject></g></g><g transform="translate(413, 2049.875)" id="flowchart-S-448" class="node default"><rect height="54" width="204" y="-27" x="-102" style="" class="basic label-container"></rect><g transform="translate(-72, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="144"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>完成前后端接口联调</p></span></div></foreignObject></g></g><g transform="translate(751, 2215.875)" id="flowchart-T-450" class="node default"><rect height="54" width="220" y="-27" x="-110" style="" class="basic label-container"></rect><g transform="translate(-80, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="160"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>用户授权功能开发完成</p></span></div></foreignObject></g></g><g transform="translate(908, 2331.875)" id="flowchart-U-456" class="node default"><rect height="78" width="236" y="-39" x="-118" style="fill:#e8f5e8 !important" class="basic label-container"></rect><g transform="translate(-88, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="176"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>小程序开发支付下单功能<br>研究微信医保支付接口</p></span></div></foreignObject></g></g><g transform="translate(328, 2331.875)" id="flowchart-V-458" class="node default"><rect height="78" width="242.625" y="-39" x="-121.3125" style="fill:#fff3e0 !important" class="basic label-container"></rect><g transform="translate(-91.3125, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="182.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>HIS系统开发支付下单接口<br>6202接口调试</p></span></div></foreignObject></g></g><g transform="translate(1043, 2459.875)" id="flowchart-W-460" class="node default"><rect height="54" width="220" y="-27" x="-110" style="" class="basic label-container"></rect><g transform="translate(-80, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="160"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>完成支付签名算法开发</p></span></div></foreignObject></g></g><g transform="translate(773, 2459.875)" id="flowchart-X-462" class="node default"><rect height="54" width="220" y="-27" x="-110" style="" class="basic label-container"></rect><g transform="translate(-80, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="160"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>完成支付跳转功能开发</p></span></div></foreignObject></g></g><g transform="translate(483, 2459.875)" id="flowchart-Y-464" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"></rect><g transform="translate(-100, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>完成与医保中台支付接口对接</p></span></div></foreignObject></g></g><g transform="translate(173, 2459.875)" id="flowchart-Z-466" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"></rect><g transform="translate(-100, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>向医保中台提供支付结果回调地址</p></span></div></foreignObject></g></g><g transform="translate(743, 2625.875)" id="flowchart-AA-468" class="node default"><rect height="54" width="188" y="-27" x="-94" style="" class="basic label-container"></rect><g transform="translate(-64, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="128"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>支付功能开发完成</p></span></div></foreignObject></g></g><g transform="translate(974.84375, 2741.875)" id="flowchart-BB-476" class="node default"><rect height="78" width="242.625" y="-39" x="-121.3125" style="fill:#fff8e1 !important" class="basic label-container"></rect><g transform="translate(-91.3125, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="182.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>HIS系统开发医保退款功能<br>6203医保退费接口</p></span></div></foreignObject></g></g><g transform="translate(229.5, 2741.875)" id="flowchart-CC-478" class="node default"><rect height="78" width="236" y="-39" x="-118" style="fill:#e3f2fd !important" class="basic label-container"></rect><g transform="translate(-88, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="176"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>小程序开发微信退款功能<br>调用微信退款接口</p></span></div></foreignObject></g></g><g transform="translate(974.84375, 2857.875)" id="flowchart-DD-480" class="node default"><rect height="54" width="236" y="-27" x="-118" style="" class="basic label-container"></rect><g transform="translate(-88, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="176"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>实现医保部分先退款逻辑</p></span></div></foreignObject></g></g><g transform="translate(229.5, 2857.875)" id="flowchart-EE-482" class="node default"><rect height="54" width="236" y="-27" x="-118" style="" class="basic label-container"></rect><g transform="translate(-88, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="176"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>实现自费部分后退款逻辑</p></span></div></foreignObject></g></g><g transform="translate(743, 2973.875)" id="flowchart-FF-484" class="node default"><rect height="78" width="236" y="-39" x="-118" style="" class="basic label-container"></rect><g transform="translate(-88, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="176"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>确保退款顺序正确<br>医保退款成功后再退微信</p></span></div></foreignObject></g></g><g transform="translate(743, 3139.875)" id="flowchart-GG-488" class="node default"><rect height="54" width="188" y="-27" x="-94" style="" class="basic label-container"></rect><g transform="translate(-64, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="128"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>所有功能开发完成</p></span></div></foreignObject></g></g><g transform="translate(743, 3255.875)" id="flowchart-HH-490" class="node default"><rect height="78" width="204" y="-39" x="-102" style="" class="basic label-container"></rect><g transform="translate(-72, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="144"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>内部功能测试<br>验证各模块功能正常</p></span></div></foreignObject></g></g><g transform="translate(743, 3383.875)" id="flowchart-II-492" class="node default"><rect height="78" width="220" y="-39" x="-110" style="" class="basic label-container"></rect><g transform="translate(-80, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="160"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>与省医保中台联调测试<br>测试医保相关接口</p></span></div></foreignObject></g></g><g transform="translate(743, 3511.875)" id="flowchart-JJ-494" class="node default"><rect height="78" width="204" y="-39" x="-102" style="" class="basic label-container"></rect><g transform="translate(-72, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="144"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>与微信医保联调测试<br>测试支付和授权功能</p></span></div></foreignObject></g></g><g transform="translate(743, 3639.875)" id="flowchart-KK-496" class="node default"><rect height="78" width="220" y="-39" x="-110" style="" class="basic label-container"></rect><g transform="translate(-80, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="160"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>端到端完整流程测试<br>模拟真实用户支付场景</p></span></div></foreignObject></g></g><g transform="translate(743, 3806.8125)" id="flowchart-LL-498" class="node default"><polygon transform="translate(-77.9375,77.9375)" class="label-container" points="77.9375,0 155.875,-77.9375 77.9375,-155.875 0,-77.9375"></polygon><g transform="translate(-50.9375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="101.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>所有测试通过?</p></span></div></foreignObject></g></g><g transform="translate(529, 3985.75)" id="flowchart-MM-500" class="node default"><rect height="54" width="172" y="-27" x="-86" style="" class="basic label-container"></rect><g transform="translate(-56, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>修复发现的问题</p></span></div></foreignObject></g></g><g transform="translate(743, 3985.75)" id="flowchart-NN-504" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>测试阶段完成</p></span></div></foreignObject></g></g><g transform="translate(743, 4151.75)" id="flowchart-OO-506" class="node default"><rect height="78" width="220" y="-39" x="-110" style="" class="basic label-container"></rect><g transform="translate(-80, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="160"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>录制完整支付流程视频<br>包含医保混合支付场景</p></span></div></foreignObject></g></g><g transform="translate(743, 4279.75)" id="flowchart-PP-508" class="node default"><rect height="78" width="252" y="-39" x="-126" style="" class="basic label-container"></rect><g transform="translate(-96, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="192"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>向国家医保局提交验收材料<br>申请正式环境权限</p></span></div></foreignObject></g></g><g transform="translate(743, 4419.75)" id="flowchart-QQ-510" class="node default"><rect height="102" width="260" y="-51" x="-130" style="" class="basic label-container"></rect><g transform="translate(-100, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>向微信申请医保支付正式环境<br>切换商户号到正式环境</p></span></div></foreignObject></g></g><g transform="translate(743, 4571.75)" id="flowchart-RR-512" class="node default"><rect height="102" width="260" y="-51" x="-130" style="" class="basic label-container"></rect><g transform="translate(-100, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>更新系统配置为正式环境参数<br>包括接口地址和密钥</p></span></div></foreignObject></g></g><g transform="translate(743, 4699.75)" id="flowchart-SS-514" class="node default"><rect height="54" width="188" y="-27" x="-94" style="" class="basic label-container"></rect><g transform="translate(-64, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="128"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>正式环境验证测试</p></span></div></foreignObject></g></g><g transform="translate(743, 4803.75)" id="flowchart-TT-516" class="node default"><rect height="54" width="188" y="-27" x="-94" style="fill:#c8e6c9 !important" class="basic label-container"></rect><g transform="translate(-64, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="128"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>项目正式上线运行</p></span></div></foreignObject></g></g></g></g></g></svg>