# 微信医保支付：服务商模式 vs 自有模式对比

## 1. 模式定义

### 自有模式（独立模式）
医院直接与微信医保对接，使用自己的商户号和资质进行医保支付。

### 服务商模式
通过第三方服务商（如软件公司、支付服务商）代理接入微信医保，医院作为服务商的子商户。

---

## 2. 接入方式对比

### 2.1 申请流程

#### 自有模式：
1. 医院自行申请微信小程序认证
2. 医院自行申请微信支付商户号
3. 医院自行向医保局申请资质
4. 医院自行向微信医保申请支付权限
5. 医院自行开发技术对接

#### 服务商模式：
1. 服务商已有微信支付服务商资质
2. 医院作为子商户加入服务商体系
3. 服务商代理申请微信医保权限
4. 服务商提供技术对接服务
5. 医院只需提供基本资质材料

### 2.2 技术对接

#### 自有模式：
```json
{
  "appid": "医院小程序AppID",
  "mch_id": "医院商户号",
  "key": "医院商户密钥",
  "pay_token": "医院医保支付token",
  "medical_org_code": "医院医疗机构编码"
}
```

#### 服务商模式：
```json
{
  "appid": "医院小程序AppID",
  "mch_id": "服务商商户号",
  "sub_mch_id": "医院子商户号",
  "key": "服务商商户密钥",
  "pay_token": "服务商医保支付token",
  "medical_org_code": "医院医疗机构编码"
}
```

---

## 3. 支付流程对比

### 3.1 自有模式支付流程

```
用户 → 医院小程序 → 医院后端 → 微信医保支付
                              ↓
                         医院商户号收款
```

**关键参数：**
- `mch_id`：医院自己的商户号
- `pay_token`：医院申请的医保支付token
- 签名使用医院商户密钥

### 3.2 服务商模式支付流程

```
用户 → 医院小程序 → 服务商后端 → 微信医保支付
                              ↓
                    服务商商户号代收 → 分账给医院
```

**关键参数：**
- `mch_id`：服务商商户号
- `sub_mch_id`：医院子商户号
- `pay_token`：服务商申请的医保支付token
- 签名使用服务商商户密钥

---

## 4. 详细技术差异

### 4.1 下单接口差异

#### 自有模式下单参数：
```xml
<xml>
  <appid>医院小程序AppID</appid>
  <mch_id>医院商户号</mch_id>
  <nonce_str>随机字符串</nonce_str>
  <body>订单描述</body>
  <out_trade_no>商户订单号</out_trade_no>
  <total_fee>订单金额</total_fee>
  <trade_type>JSAPI</trade_type>
  <openid>用户openid</openid>
  <medical_org_code>医疗机构编码</medical_org_code>
  <sign>签名</sign>
</xml>
```

#### 服务商模式下单参数：
```xml
<xml>
  <appid>医院小程序AppID</appid>
  <mch_id>服务商商户号</mch_id>
  <sub_mch_id>医院子商户号</sub_mch_id>
  <nonce_str>随机字符串</nonce_str>
  <body>订单描述</body>
  <out_trade_no>商户订单号</out_trade_no>
  <total_fee>订单金额</total_fee>
  <trade_type>JSAPI</trade_type>
  <sub_openid>用户在子商户下的openid</sub_openid>
  <medical_org_code>医疗机构编码</medical_org_code>
  <sign>签名</sign>
</xml>
```

### 4.2 签名算法差异

#### 自有模式：
使用医院商户密钥进行签名

#### 服务商模式：
使用服务商商户密钥进行签名

### 4.3 回调处理差异

#### 自有模式：
回调直接发送到医院服务器

#### 服务商模式：
回调发送到服务商服务器，服务商再通知医院

---

## 5. 优缺点对比

### 5.1 自有模式

#### 优点：
- **资金直接到账**：支付资金直接进入医院账户
- **数据完全自控**：所有交易数据完全掌控
- **费率可能更低**：直接对接，无中间商费用
- **技术自主性强**：完全掌控技术实现

#### 缺点：
- **申请周期长**：需要自行申请各种资质
- **技术门槛高**：需要自行开发和维护
- **风险自担**：技术风险、合规风险需自行承担
- **维护成本高**：需要专业技术团队

### 5.2 服务商模式

#### 优点：
- **接入速度快**：服务商已有资质，快速接入
- **技术门槛低**：服务商提供技术支持
- **维护成本低**：技术维护由服务商负责
- **合规风险小**：服务商承担主要合规责任

#### 缺点：
- **资金需分账**：资金先到服务商，再分账给医院
- **费率可能更高**：需要支付服务商费用
- **数据依赖性**：部分数据依赖服务商
- **技术依赖性**：技术实现依赖服务商

---

## 6. 选择建议

### 6.1 适合自有模式的医院：
- 大型三甲医院
- 有专业技术团队
- 交易量大，对费率敏感
- 对数据安全要求极高
- 有充足的开发时间

### 6.2 适合服务商模式的医院：
- 中小型医院
- 技术团队有限
- 希望快速上线
- 交易量相对较小
- 更关注业务而非技术

---

## 7. 成本对比

### 7.1 自有模式成本：
- **开发成本**：技术团队开发费用
- **维护成本**：持续技术维护
- **合规成本**：安全认证、合规审计
- **交易费率**：微信支付标准费率

### 7.2 服务商模式成本：
- **服务费**：支付给服务商的服务费
- **交易费率**：可能略高于标准费率
- **集成成本**：与服务商系统集成
- **依赖风险**：服务商变更的潜在成本

---

## 总结

**自有模式**适合有技术实力、交易量大的医院，可以获得更好的费率和完全的数据控制权。

**服务商模式**适合希望快速接入、技术实力有限的医院，可以降低技术门槛和开发成本。

选择哪种模式主要取决于医院的技术实力、交易规模、时间要求和成本考虑。
