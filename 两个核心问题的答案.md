# 微信医保支付对接两个核心问题

## 问题1：对接微信和医保局的接口，我必须要提供哪些信息，而这些信息我应该如何获得？

### 必须提供的信息：

#### 微信方面：
- **小程序AppID**：微信小程序后台获取
- **商户号**：微信商户平台申请（必须是微信平台申请的）
- **商户密钥**：微信商户平台设置
- **API证书**：微信商户平台下载

#### 医保方面：
- **医疗机构编码**：医保局审核通过后分配
- **支付token**：微信医保审核通过后邮件发送

#### 用户相关：
- **payAuthNo**：用户授权后获得
- **用户经纬度**：小程序获取用户位置

### 如何获得：
1. **先申请微信小程序认证**（以医院名义）
2. **申请微信支付商户号**
3. **向医保局申请医疗机构资质**
4. **向微信医保申请支付权限**（需要前面的资质）

---

## 问题2：测试开发环境，我们应该如何对接，需要准备什么？

### 需要准备：

#### 环境准备：
- **测试服务器**：有外网IP，配置HTTPS
- **测试域名**：备案的域名
- **测试数据库**：存储订单和配置信息

#### 账号准备：
- **测试微信号**：加入白名单
- **测试医保用户**：有医保资格的测试账号
- **测试订单数据**：在HIS系统中准备测试处方

#### 配置参数：
```json
{
  "appid": "测试小程序AppID",
  "mch_id": "测试商户号", 
  "key": "测试商户密钥",
  "pay_token": "测试支付token",
  "medical_org_code": "医疗机构编码"
}
```

#### 开发内容：
1. **小程序端**：用户登录、位置获取、支付跳转
2. **后端接口**：订单查询、支付下单、结果回调
3. **HIS对接**：获取订单数据、更新支付状态

### 对接步骤：
1. **搭建测试环境**
2. **配置参数**
3. **开发接口**
4. **联调测试**

---

**总结**：
- 信息获取：按顺序申请微信小程序→微信支付→医保资质→微信医保权限
- 测试环境：准备服务器、账号、配置、开发小程序和后端接口
